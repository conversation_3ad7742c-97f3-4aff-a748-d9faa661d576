<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="80f83ada-4361-44d0-9523-4f062f7d3828" name="变更" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shrcb/sqlmonitor/db.go" beforeDir="false" afterPath="$PROJECT_DIR$/shrcb/sqlmonitor/db.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shrcb/sqlmonitor/index.go" beforeDir="false" afterPath="$PROJECT_DIR$/shrcb/sqlmonitor/index.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shrcb/sqlmonitor/model.go" beforeDir="false" afterPath="$PROJECT_DIR$/shrcb/sqlmonitor/model.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/go/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="31DbkjxU9FK3FufADFOa5mQxQET" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="configurable..is.expanded" value="false" />
    <property name="configurable.GoLibrariesConfigurable.is.expanded" value="true" />
    <property name="go.formatter.settings.were.checked" value="true" />
    <property name="go.import.settings.migrated" value="true" />
    <property name="go.modules.go.list.on.any.changes.was.set" value="true" />
    <property name="go.sdk.automatically.set" value="true" />
    <property name="go.watchers.conflict.with.on.save.actions.check.performed" value="true" />
    <property name="last_opened_file_path" value="$USER_HOME$" />
    <property name="settings.editor.selected.configurable" value="com.goide.configuration.GoLibrariesConfigurableProvider" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <integration-enabled>true</integration-enabled>
  </component>
</project>